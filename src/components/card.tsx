import { Image, StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { Article } from '../utils/type';
import UserAvatar from './UserAvatar';

const Card: React.FC<{ article: Article }> = ({ article }) => {
  return (
    <>
      <View
        style={{
          justifyContent: 'flex-end',
          alignItems: 'flex-end',
          marginBottom: 4,
        }}
      >
        <UserAvatar userName={article.userName} />
      </View>
      <View style={styles.card}>
        {/* Main Image */}
        <Image source={{ uri: article.mainImage }} style={styles.mainImage} />

        {/* Footer Section */}
        <View style={styles.footer}>
          <View style={styles.info}>
            <Image source={{ uri: article.logo }} style={styles.logo} />
            <View>
              <Text style={styles.title}>{article.title}</Text>
              <Text style={styles.subTitle}>{article.subTitle}</Text>
            </View>
          </View>

          <TouchableOpacity style={styles.button}>
            <Text style={styles.buttonText}>Refresh</Text>
          </TouchableOpacity>
        </View>

        {/* 
      <View style={styles.author}>
        <Image source={{ uri: article.thumbNailImage }} style={styles.avatar} />
        <Text style={styles.userName}>{article.userName}</Text>
      </View>

      <ScrollView style={styles.htmlContainer}>
        <RenderHTML contentWidth={width} source={{ html: article.text }} />
      </ScrollView> */}
      </View>
    </>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#fff',
    paddingTop: 50,
    paddingHorizontal: 16,
  },
  header: {
    fontSize: 28,
    fontWeight: '700',
    marginBottom: 20,
  },
  card: {
    backgroundColor: '#fff',
    borderRadius: 20,
    shadowColor: '#000',
    shadowOpacity: 0.1,
    shadowRadius: 10,
    shadowOffset: { width: 0, height: 4 },
    elevation: 5,
    overflow: 'hidden',
  },
  mainImage: {
    width: '100%',
    height: 250,
  },
  footer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 16,
  },
  info: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  logo: {
    width: 40,
    height: 40,
    borderRadius: 8,
    marginRight: 10,
  },
  title: {
    fontSize: 14,
    fontWeight: '600',
  },
  subTitle: {
    fontSize: 12,
    color: '#666',
  },
  button: {
    backgroundColor: '#f1f1ff',
    paddingHorizontal: 16,
    paddingVertical: 6,
    borderRadius: 12,
  },
  buttonText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#6c63ff',
  },
  author: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 16,
    paddingBottom: 10,
  },
  avatar: {
    width: 28,
    height: 28,
    borderRadius: 14,
    marginRight: 8,
  },
  userName: {
    fontSize: 12,
    fontWeight: '500',
    color: '#333',
  },
  htmlContainer: {
    padding: 16,
    maxHeight: 300, // to keep scrollable content controlled
  },
});

export default Card;
