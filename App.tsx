import { StatusBar } from 'react-native';
import { SafeAreaProvider } from 'react-native-safe-area-context';
import Card from './src/components/card';

const articleData = {
  thumbNailImage:
    'https://e0.pxfuel.com/wallpapers/847/217/desktop-wallpaper-android-luffy-ace-sabo-cool-ace-one-piece-thumbnail.jpg',
  mainImage:
    'https://i.pinimg.com/236x/23/79/48/237948a6d5088270501c2918a3b27d11.jpg',
  userName: 'Ankit Hooda',
  subTitle: 'There are many variations of passages',
  text: `
      <html>
        <head>
          <title> One Piece </title>
        </head>
        <body>
          <p>
            There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomised words which don't look even slightly believable. If you are going to use a passage of Lorem Ipsum, you need to be sure there isn't anything embarrassing hidden in the middle of text.
          </p>
          <p>
            There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomised words which don't look even slightly believable. If you are going to use a passage of Lorem Ipsum, you need to be sure there isn't anything embarrassing hidden in the middle of text.
          </p>
          <p>
            Lorem Ipsum is simply dummy text of the printing and typesetting industry. Lorem Ipsum has been the industry's standard dummy text ever since the 1500s, when an unknown printer took a galley of type and scrambled it to make a type specimen book. It has survived not only five centuries, but also the leap into electronic typesetting, remaining essentially unchanged. It was popularised in the 1960s with the release of Letraset sheets containing Lorem Ipsum passages, and more recently with desktop publishing software like Aldus PageMaker including versions of Lorem Ipsum.
          </p>
          <p>
            There are many variations of passages of Lorem Ipsum available, but the majority have suffered alteration in some form, by injected humour, or randomised words which don't look even slightly believable. If you are going to use a passage of Lorem Ipsum, you need to be sure there isn't anything embarrassing hidden in the middle of text. All the Lorem Ipsum generators on the Internet tend to repeat predefined chunks as necessary, making this the first true generator on the Internet. It uses a dictionary of over 200 Latin words, combined with a handful of model sentence structures, to generate Lorem Ipsum which looks reasonable. The generated Lorem Ipsum is therefore always free from repetition, injected humour, or non-characteristic words etc.
          </p>
        </body>
      </html>
    `,
  id: 2,
  logo: 'https://i.pinimg.com/originals/e7/9c/df/e79cdfc22bbbd73435ec83e9d1f05bc4.jpg',
  title: 'One Piece',
};
function App() {
  return (
    <SafeAreaProvider style={{ backgroundColor: '#fff' }}>
      <StatusBar barStyle={'light-content'} />
      <Card article={articleData} />
    </SafeAreaProvider>
  );
}

export default App;
